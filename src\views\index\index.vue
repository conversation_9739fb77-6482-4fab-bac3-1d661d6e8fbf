<template>
  <div id="index" ref="appRef">
    <div class="bg">
      <dv-loading v-if="loading">Loading...</dv-loading>
      <div v-else>
        <!-- title -->
        <div class="titleText">{{ title }}</div>
        <!-- logo -->
        <div style="position: absolute; top: 50px; left: 44px">
          <img src="@/assets/logoImg.png" alt="" style="width: 174px; height: 40px" />
        </div>
        <div class="timeText">
          <div class="timet">
            {{ dateData.dateDay }}
          </div>
          <div class="timeb">
            <span>{{ dateData.dateYear }}</span
            ><span style="margin-left: 4px">{{ dateData.dateWeek }}</span>
          </div>
        </div>

        <!-- left -->
        <div class="leftContent">
          <div class="left_top">
            <div>
              <img src="@/assets/leftTop.png" style="width: 480px; height: 29px" />
            </div>

            <centerTop />
          </div>
          <div class="left_center">
            <div>
              <img src="@/assets/leftCenter.png" style="width: 480px; height: 29px" />
            </div>
            <div class="contont">

              <centerLeft />
            </div>
          </div>
          <div class="left_bottom">
            <img
              src="@/assets/leftBottom.png"
              style="width: 480px; height: 29px; position: absolute" />
            <bottomLeft />
          </div>
        </div>
        <!-- center -->
        <div class="centerContent">
          <div class="center_leftTop_title">今年入库单</div>
          <div class="center_leftTop_num">
            <n-number-animation
              ref="numberAnimationInstRef"
              :from="0"
              :to="centerNumber.r1" />
          </div>
        </div>
        <div class="centerContent" style="left: 1180px">
          <div class="center_leftTop_title">今年出库单</div>
          <div class="center_leftTop_num">
            <n-number-animation
              ref="numberAnimationInstRef"
              :from="0"
              :to="centerNumber.r2" />
          </div>
        </div>
        <div class="centerContent" style="top: 504px">
          <div class="center_leftTop_title">物资类别（一级）</div>
          <div class="center_leftTop_num">
            <n-number-animation
              ref="numberAnimationInstRef"
              :from="0"
              :to="centerNumber.r3" />
          </div>
        </div>
        <div class="centerContent" style="left: 1180px; top: 504px">
          <div class="center_leftTop_title">物资目录</div>
          <div class="center_leftTop_num">
            <n-number-animation
              ref="numberAnimationInstRef"
              :from="0"
              :to="centerNumber.r4" />
          </div>
        </div>
        <!-- right -->
        <div class="rightContent">
          <div class="right_top">
            <img
              src="@/assets/arrow.png"
              style="position: absolute; right: 20px; top: 7px; width: 16px; height: 20px"
              @click="monitorClick" />
            <div>
              <img src="@/assets/monitor.png" style="width: 480px; height: 29px" />
            </div>
            <div class="contont">
              <div class="video"></div>
            </div>
          </div>
          <div class="right_center">
            <img
              src="@/assets/temperature.png"
              style="width: 480px; height: 29px; position: absolute" />
            <centerRight />
          </div>
          <div class="right_bottom">
            <div>
              <img src="@/assets/expire.png" style="width: 480px; height: 29px" />
            </div>
            <div class="contont">
              <bottomRight />
            </div>
          </div>
        </div>

        <!-- bottom -->
        <div class="bottomContent">
          <div><img src="@/assets/bottom.png" style="width: 812px; height: 29px" /></div>

          <div class="bottom_top">
            <centerBottom />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { formatTime } from '@/utils/index'
import useDraw from '@/utils/useDraw'
import { NNumberAnimation } from 'naive-ui'

defineComponent({
  components: {
    NNumberAnimation,
  },
})
//仓库基本信息
import centerTop from '../topLeft/index.vue'
//出库频率
import centerLeft from '../centerLeft/index.vue'
//温湿度曲线
import centerRight from '../centerRight/index.vue'
//出入库统计
import bottomLeft from '../bottomLeft/index.vue'
//库存预警率
import centerBottom from '../centerBottom/index.vue'

//过期
import bottomRight from '../bottomRight/index.vue'
//title
let title = ref('智慧戎仓大数据智控平台')
//leftTop_logo

//rightTop_time
const dateData = reactive<any>({
  dateDay: '',
  dateYear: [],
  dateWeek: '',
  timing: null,
})

const weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
const timeFn = () => {
  dateData.timing = setInterval(() => {
    const time = formatTime(new Date(), 'HH:mm:ss')
    dateData.dateDay = time
    const month =
      new Date().getMonth() + 1 < 10
        ? '0' + (new Date().getMonth() + 1)
        : new Date().getMonth() + 1
    const day =
      new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
    const year = new Date().getFullYear() + '.' + month + '.' + day
    dateData.dateYear = year
    //获取星期几
    const week = weekday[new Date().getDay()]
    dateData.dateWeek = week
  }, 16)
}

//center
const centerNumber = reactive({
  r1: '71438',
  r2: '97783',
  r3: '3036',
  r4: '300',
})
//导入路由
const router = useRouter()
//监控
const monitorClick = () => {
  console.log('监控')
  //路由跳转
  router.push('/monitor')
}

// * 颜色
// * 加载标识
const loading = ref<boolean>(true)

// * 适配处理
const { appRef, calcRate, windowDraw, unWindowDraw } = useDraw()
// 生命周期
onMounted(() => {
  timeFn()

  cancelLoading()
  // todo 屏幕适应
  windowDraw()
  calcRate()
})

onUnmounted(() => {
  unWindowDraw()
  clearInterval(dateData.timing)
})

// methods
const cancelLoading = () => {
  setTimeout(() => {
    loading.value = false
  }, 500)
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/index.scss';
// title
.titleText {
  position: absolute;
  top: 18px;
  left: 50%;
  transform: translateX(-50%);
  font-family: 'alim';
  font-size: 44px;
  font-style: normal;
  line-height: 60px;
  background: linear-gradient(180deg, #a2ffda 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//time
.timeText {
  position: absolute;
  top: 40px;
  right: 50px;
  color: #c0dcde;

  width: 130px;
  height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .timet {
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .timeb {
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
//right
.leftContent {
  position: absolute;
  bottom: 24px;
  left: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #c0dcde;
  width: 480px;
  height: 933px;
  .left_top {
    width: 480px;
    height: 300px;
    .contont {
      margin-top: 4px;
      width: 480px;
      height: 267px;
      background-color: pink;
    }
  }
  .left_center {
    margin-top: 16px;
    margin-bottom: 16px;
    width: 480px;
    height: 300px;
    .contont {
      margin-top: 4px;
      width: 480px;
      height: 267px;
      padding-left: 12px;
      padding-right: 12px;
      padding-top: 16px;
      padding-bottom: 16px;
      box-sizing: border-box;
    }
  }
  .left_bottom {
    width: 480px;
    height: 300px;
    position: relative;
  }
}
// center
.centerContent {
  position: absolute;
  top: 174px;
  left: 644px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0dcde;

  width: 100px;
  .center_leftTop_title {
    width: 130px;
    text-align: center;
    font-family: 'alia';
    font-size: 16px;
    font-style: normal;
    font-weight: 55 Regular;
    line-height: normal;
  }
  .center_leftTop_num {
    text-align: center;
    font-family: 'roboto';
    font-size: 36px;
    font-weight: Condensed SemiBold Italic;
    line-height: normal;
    letter-spacing: 0.72px;
    background: linear-gradient(180deg, #ffcd61 0%, #ffe0a6 75.72%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

//right
.rightContent {
  position: absolute;
  bottom: 24px;
  right: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #c0dcde;
  width: 480px;
  height: 932px;
  .right_top {
    width: 480px;
    height: 300px;
    position: relative;
    //居中
    .contont {
      margin-top: 4px;
      width: 448px;
      height: 252px;
      margin-left: 16px;
      background-image: url('@/assets/kuang.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding-top: 7px;
      box-sizing: border-box;
      .video {
        width: 436px;
        height: 238px;
        margin-left: 6px;
        // background-color: pink;
      }
    }
  }
  .right_center {
    margin-top: 16px;
    margin-bottom: 16px;
    width: 480px;
    height: 300px;
    position: relative;
  }
  .right_bottom {
    width: 480px;
    height: 300px;


    .contont {
      margin-top: 4px;
      width: 480px;
      height: 267px;
      padding-left: 12px;
      padding-right: 12px;
      padding-top: 16px;
      padding-bottom: 16px;
      box-sizing: border-box;

    }
  }
}

//bottom
.bottomContent {
  position: absolute;
  bottom: 24px;
  left: 554px;
  flex-direction: column;
  align-items: center;
  width: 812px;
  height: 300px;
  .bottom_top {
    margin-top: 10px;
    width: 812px;
    height: 267px;
  }
}
</style>
